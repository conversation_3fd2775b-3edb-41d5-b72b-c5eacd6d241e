"""
Sitemap Parsing Service using ultimate-sitemap-parser
This service provides sitemap extraction and URL discovery capabilities
"""

import logging
import requests
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from usp.tree import sitemap_tree_for_homepage
from usp.fetch_parse import SitemapFetcher
import xml.etree.ElementTree as ET
from django.conf import settings

logger = logging.getLogger(__name__)


class SitemapParsingService:
    """Service for sitemap parsing and URL extraction"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def _is_sitemap_index(self, sitemap_url: str) -> bool:
        """
        Check if a sitemap URL is a sitemap index file

        Args:
            sitemap_url: URL to check

        Returns:
            True if it's a sitemap index, False otherwise
        """
        try:
            response = self.session.get(sitemap_url, timeout=10)
            if response.status_code == 200:
                # Parse XML to check if it's a sitemap index
                root = ET.fromstring(response.content)
                # Check if root element is sitemapindex
                return root.tag.endswith('sitemapindex')
        except Exception as e:
            logger.debug(
                f"Could not determine if {sitemap_url} is sitemap index: {str(e)}")
        return False

    def _extract_sitemap_urls_from_index(self, sitemap_index_url: str) -> List[str]:
        """
        Extract individual sitemap URLs from a sitemap index

        Args:
            sitemap_index_url: URL of the sitemap index

        Returns:
            List of individual sitemap URLs
        """
        try:
            response = self.session.get(sitemap_index_url, timeout=10)
            response.raise_for_status()

            root = ET.fromstring(response.content)
            sitemap_urls = []

            # Find all sitemap elements and extract loc tags
            for sitemap in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}sitemap'):
                loc = sitemap.find(
                    '{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                if loc is not None and loc.text:
                    sitemap_urls.append(loc.text.strip())

            logger.info(
                f"Found {len(sitemap_urls)} individual sitemaps in index")
            return sitemap_urls

        except Exception as e:
            logger.error(
                f"Error extracting sitemap URLs from index {sitemap_index_url}: {str(e)}")
            return []

    def extract_sitemap_urls(self, sitemap_url: str, max_urls: int = 100) -> List[Dict[str, Any]]:
        """
        Extract URLs from a sitemap with metadata
        Handles both regular sitemaps and sitemap index files

        Args:
            sitemap_url: URL of the sitemap or sitemap index
            max_urls: Maximum number of URLs to return

        Returns:
            List of dictionaries containing URL information
        """
        try:
            logger.info(f"Extracting URLs from sitemap: {sitemap_url}")

            # Check if this is a sitemap index
            if self._is_sitemap_index(sitemap_url):
                logger.info(f"Detected sitemap index: {sitemap_url}")

                # Extract individual sitemap URLs from the index
                individual_sitemaps = self._extract_sitemap_urls_from_index(
                    sitemap_url)

                if not individual_sitemaps:
                    logger.warning(
                        f"No individual sitemaps found in index: {sitemap_url}")
                    return []

                # Extract URLs from all individual sitemaps
                all_urls = []
                urls_per_sitemap = max(
                    1, max_urls // len(individual_sitemaps)) if max_urls else 50

                for individual_sitemap in individual_sitemaps:
                    try:
                        logger.info(
                            f"Processing individual sitemap: {individual_sitemap}")
                        fetcher = SitemapFetcher(
                            individual_sitemap, recursion_level=0)
                        sitemap = fetcher.sitemap()

                        # Limit URLs from this sitemap
                        sitemap_urls = sitemap.pages[:urls_per_sitemap] if urls_per_sitemap else sitemap.pages

                        for page in sitemap_urls:
                            url_data = {
                                'url': page.url,
                                'last_modified': page.last_modified.isoformat() if page.last_modified else None,
                                'change_frequency': page.change_frequency,
                                'priority': page.priority
                            }
                            all_urls.append(url_data)

                            # Stop if we've reached the max_urls limit
                            if max_urls and len(all_urls) >= max_urls:
                                break

                    except Exception as e:
                        logger.warning(
                            f"Failed to process individual sitemap {individual_sitemap}: {str(e)}")
                        continue

                    # Stop if we've reached the max_urls limit
                    if max_urls and len(all_urls) >= max_urls:
                        break

                # Final limit check
                if max_urls and len(all_urls) > max_urls:
                    all_urls = all_urls[:max_urls]

                logger.info(
                    f"Successfully extracted {len(all_urls)} URLs from sitemap index")
                return all_urls

            else:
                # Regular sitemap processing
                fetcher = SitemapFetcher(sitemap_url, recursion_level=0)
                sitemap = fetcher.sitemap()
                urls = sitemap.pages

                # Limit the number of URLs
                if max_urls and len(urls) > max_urls:
                    urls = urls[:max_urls]

                # Convert to list of dictionaries with metadata
                result = []
                for page in urls:
                    url_data = {
                        'url': page.url,
                        'last_modified': page.last_modified.isoformat() if page.last_modified else None,
                        'change_frequency': page.change_frequency,
                        'priority': page.priority
                    }
                    result.append(url_data)

                logger.info(
                    f"Successfully extracted {len(result)} URLs from sitemap")
                return result

        except Exception as e:
            logger.error(
                f"Error extracting URLs from sitemap {sitemap_url}: {str(e)}")
            raise

    def discover_sitemap_urls(self, base_url: str) -> List[str]:
        """
        Discover sitemap URLs for a website

        Args:
            base_url: Base URL of the website

        Returns:
            List of discovered sitemap URLs
        """
        try:
            logger.info(f"Discovering sitemaps for: {base_url}")

            # Common sitemap locations
            potential_sitemaps = [
                urljoin(base_url, '/sitemap.xml'),
                urljoin(base_url, '/sitemap_index.xml'),
                urljoin(base_url, '/sitemaps.xml'),
                urljoin(base_url, '/sitemap/sitemap.xml'),
                urljoin(base_url, '/wp-sitemap.xml'),  # WordPress
                urljoin(base_url, '/sitemap-index.xml'),
            ]

            discovered_sitemaps = []

            # Check each potential sitemap URL
            for sitemap_url in potential_sitemaps:
                try:
                    response = self.session.head(sitemap_url, timeout=10)
                    if response.status_code == 200:
                        discovered_sitemaps.append(sitemap_url)
                        logger.info(f"Found sitemap: {sitemap_url}")
                except Exception as e:
                    logger.debug(
                        f"Sitemap not found at {sitemap_url}: {str(e)}")
                    continue

            # Check robots.txt for sitemap declarations
            try:
                robots_url = urljoin(base_url, '/robots.txt')
                response = self.session.get(robots_url, timeout=10)
                if response.status_code == 200:
                    for line in response.text.split('\n'):
                        line = line.strip()
                        if line.lower().startswith('sitemap:'):
                            sitemap_url = line.split(':', 1)[1].strip()
                            if sitemap_url not in discovered_sitemaps:
                                discovered_sitemaps.append(sitemap_url)
                                logger.info(
                                    f"Found sitemap in robots.txt: {sitemap_url}")
            except Exception as e:
                logger.debug(
                    f"Could not check robots.txt for {base_url}: {str(e)}")

            logger.info(
                f"Discovered {len(discovered_sitemaps)} sitemaps for {base_url}")
            return discovered_sitemaps

        except Exception as e:
            logger.error(
                f"Error discovering sitemaps for {base_url}: {str(e)}")
            raise

    def analyze_website_structure(self, base_url: str, max_urls_per_sitemap: int = 50) -> Dict[str, Any]:
        """
        Analyze website structure by discovering and parsing all sitemaps

        Args:
            base_url: Base URL of the website
            max_urls_per_sitemap: Maximum URLs to extract from each sitemap

        Returns:
            Dictionary containing complete website structure analysis
        """
        try:
            logger.info(f"Analyzing website structure for: {base_url}")

            # Discover all sitemaps
            sitemap_urls = self.discover_sitemap_urls(base_url)

            if not sitemap_urls:
                return {
                    'base_url': base_url,
                    'sitemaps_found': 0,
                    'total_urls': 0,
                    'sitemaps': [],
                    'all_urls': []
                }

            # Extract URLs from each sitemap
            all_sitemaps_data = []
            all_urls = []

            for sitemap_url in sitemap_urls:
                try:
                    sitemap_data = {
                        'sitemap_url': sitemap_url,
                        'urls': self.extract_sitemap_urls(sitemap_url, max_urls_per_sitemap)
                    }
                    all_sitemaps_data.append(sitemap_data)

                    # Collect all URLs
                    for url_info in sitemap_data['urls']:
                        if url_info['url'] not in [u['url'] for u in all_urls]:
                            all_urls.append(url_info)

                except Exception as e:
                    logger.warning(
                        f"Failed to parse sitemap {sitemap_url}: {str(e)}")
                    continue

            # Analyze URL patterns
            url_patterns = self._analyze_url_patterns(all_urls)

            result = {
                'base_url': base_url,
                'sitemaps_found': len(sitemap_urls),
                'total_urls': len(all_urls),
                'sitemaps': all_sitemaps_data,
                'all_urls': all_urls,
                'url_patterns': url_patterns
            }

            logger.info(
                f"Website analysis complete: {len(all_urls)} total URLs from {len(sitemap_urls)} sitemaps")
            return result

        except Exception as e:
            logger.error(
                f"Error analyzing website structure for {base_url}: {str(e)}")
            raise

    def _analyze_url_patterns(self, urls: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze URL patterns to understand website structure

        Args:
            urls: List of URL dictionaries

        Returns:
            Dictionary containing URL pattern analysis
        """
        patterns = {
            'total_urls': len(urls),
            'domains': set(),
            'path_patterns': {},
            'file_extensions': {},
            'url_depths': {}
        }

        for url_info in urls:
            url = url_info['url']
            parsed = urlparse(url)

            # Track domains
            patterns['domains'].add(parsed.netloc)

            # Analyze path patterns
            path_parts = [part for part in parsed.path.split('/') if part]
            depth = len(path_parts)

            # Track URL depth
            patterns['url_depths'][depth] = patterns['url_depths'].get(
                depth, 0) + 1

            # Track file extensions
            if '.' in parsed.path:
                extension = parsed.path.split('.')[-1].lower()
                if len(extension) <= 5:  # Reasonable extension length
                    patterns['file_extensions'][extension] = patterns['file_extensions'].get(
                        extension, 0) + 1

            # Track path patterns (first level)
            if path_parts:
                first_level = path_parts[0]
                patterns['path_patterns'][first_level] = patterns['path_patterns'].get(
                    first_level, 0) + 1

        # Convert sets to lists for JSON serialization
        patterns['domains'] = list(patterns['domains'])

        return patterns


# Global service instance
sitemap_service = SitemapParsingService()


def get_website_sitemap_analysis(base_url: str, max_urls: int = 100) -> Dict[str, Any]:
    """
    Standalone function to get complete sitemap analysis for a website

    Args:
        base_url: Base URL of the website to analyze
        max_urls: Maximum URLs to extract per sitemap

    Returns:
        Dictionary containing complete sitemap analysis
    """
    return sitemap_service.analyze_website_structure(base_url, max_urls)


def extract_urls_from_sitemap(sitemap_url: str, max_urls: int = 100) -> List[Dict[str, Any]]:
    """
    Standalone function to extract URLs from a specific sitemap

    Args:
        sitemap_url: Direct URL to the sitemap
        max_urls: Maximum number of URLs to extract

    Returns:
        List of URL dictionaries with metadata
    """
    return sitemap_service.extract_sitemap_urls(sitemap_url, max_urls)


def discover_website_sitemaps(base_url: str) -> List[str]:
    """
    Standalone function to discover all sitemaps for a website

    Args:
        base_url: Base URL of the website

    Returns:
        List of discovered sitemap URLs
    """
    return sitemap_service.discover_sitemap_urls(base_url)
