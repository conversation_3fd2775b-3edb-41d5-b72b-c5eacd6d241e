from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import json


class AgentProfile(models.Model):
    """Model to store AutoGen agent configurations"""
    AGENT_TYPES = [
        ('assistant', 'Assistant Agent'),
        ('user_proxy', 'User Proxy Agent'),
        ('group_chat_manager', 'Group Chat Manager'),
        ('custom', 'Custom Agent'),
    ]

    name = models.CharField(max_length=100, unique=True)
    agent_type = models.CharField(
        max_length=20, choices=AGENT_TYPES, default='assistant')
    system_message = models.TextField(help_text="System message for the agent")
    model_config = models.JSONField(
        default=dict, help_text="Model configuration (API keys, model name, etc.)")
    tools = models.JSONField(
        default=list, help_text="List of tools/functions available to the agent")
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.agent_type})"


class Team(models.Model):
    """Model to store AutoGen team configurations"""
    TEAM_TYPES = [
        ('round_robin', 'Round Robin'),
        ('selector_group_chat', 'Selector Group Chat'),
        ('swarm', 'Swarm'),
        ('graph_flow', 'Graph Flow'),
        ('custom', 'Custom'),
    ]

    name = models.CharField(max_length=100, unique=True)
    team_type = models.CharField(
        max_length=20, choices=TEAM_TYPES, default='round_robin')
    description = models.TextField(blank=True)
    agents = models.ManyToManyField(AgentProfile, through='TeamMembership')
    termination_config = models.JSONField(
        default=dict, help_text="Termination conditions")
    max_turns = models.IntegerField(
        default=10, help_text="Maximum number of conversation turns")
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.team_type})"


class TeamMembership(models.Model):
    """Through model for Team-Agent relationship with additional metadata"""
    team = models.ForeignKey(Team, on_delete=models.CASCADE)
    agent = models.ForeignKey(AgentProfile, on_delete=models.CASCADE)
    role = models.CharField(max_length=50, default='member')
    order = models.IntegerField(default=0, help_text="Order in team execution")
    is_selector = models.BooleanField(
        default=False, help_text="Is this agent a selector in group chat")

    class Meta:
        unique_together = ['team', 'agent']
        ordering = ['order']

    def __str__(self):
        return f"{self.team.name} - {self.agent.name} ({self.role})"


class Conversation(models.Model):
    """Model to store conversation sessions"""
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    title = models.CharField(max_length=200)
    team = models.ForeignKey(Team, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='active')
    initial_message = models.TextField(
        help_text="Initial message that started the conversation")
    final_result = models.TextField(
        blank=True, help_text="Final result or summary")
    metadata = models.JSONField(
        default=dict, help_text="Additional metadata about the conversation")
    started_by = models.ForeignKey(User, on_delete=models.CASCADE)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-started_at']

    def __str__(self):
        return f"{self.title} ({self.status})"

    def duration(self):
        if self.completed_at:
            return self.completed_at - self.started_at
        return timezone.now() - self.started_at


class Message(models.Model):
    """Model to store individual messages in conversations"""
    MESSAGE_TYPES = [
        ('user', 'User Message'),
        ('assistant', 'Assistant Message'),
        ('system', 'System Message'),
        ('tool_call', 'Tool Call'),
        ('tool_result', 'Tool Result'),
    ]

    conversation = models.ForeignKey(
        Conversation, on_delete=models.CASCADE, related_name='messages')
    sender_agent = models.ForeignKey(
        AgentProfile, on_delete=models.CASCADE, null=True, blank=True)
    message_type = models.CharField(
        max_length=20, choices=MESSAGE_TYPES, default='assistant')
    content = models.TextField()
    metadata = models.JSONField(
        default=dict, help_text="Message metadata (tokens, model used, etc.)")
    sequence_number = models.IntegerField(
        help_text="Order of message in conversation")
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['sequence_number']
        unique_together = ['conversation', 'sequence_number']

    def __str__(self):
        sender = self.sender_agent.name if self.sender_agent else "System"
        return f"{sender}: {self.content[:50]}..."


class Task(models.Model):
    """Model to store and track tasks for agents"""
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField()
    assigned_team = models.ForeignKey(
        Team, on_delete=models.CASCADE, null=True, blank=True)
    assigned_agent = models.ForeignKey(
        AgentProfile, on_delete=models.CASCADE, null=True, blank=True)
    priority = models.CharField(
        max_length=10, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='pending')
    result = models.TextField(blank=True, help_text="Task execution result")
    conversation = models.ForeignKey(
        Conversation, on_delete=models.SET_NULL, null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    due_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} ({self.status})"
