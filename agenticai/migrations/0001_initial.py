# Generated by Django 5.2.4 on 2025-07-09 07:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AgentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('agent_type', models.CharField(choices=[('assistant', 'Assistant Agent'), ('user_proxy', 'User Proxy Agent'), ('group_chat_manager', 'Group Chat Manager'), ('custom', 'Custom Agent')], default='assistant', max_length=20)),
                ('system_message', models.TextField(help_text='System message for the agent')),
                ('model_config', models.J<PERSON><PERSON>ield(default=dict, help_text='Model configuration (API keys, model name, etc.)')),
                ('tools', models.J<PERSON><PERSON><PERSON>(default=list, help_text='List of tools/functions available to the agent')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('initial_message', models.TextField(help_text='Initial message that started the conversation')),
                ('final_result', models.TextField(blank=True, help_text='Final result or summary')),
                ('metadata', models.JSONField(default=dict, help_text='Additional metadata about the conversation')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('started_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('team_type', models.CharField(choices=[('round_robin', 'Round Robin'), ('selector_group_chat', 'Selector Group Chat'), ('swarm', 'Swarm'), ('graph_flow', 'Graph Flow'), ('custom', 'Custom')], default='round_robin', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('termination_config', models.JSONField(default=dict, help_text='Termination conditions')),
                ('max_turns', models.IntegerField(default=10, help_text='Maximum number of conversation turns')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('result', models.TextField(blank=True, help_text='Task execution result')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('due_date', models.DateTimeField(blank=True, null=True)),
                ('assigned_agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='agenticai.agentprofile')),
                ('conversation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='agenticai.conversation')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('assigned_team', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='agenticai.team')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='conversation',
            name='team',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='agenticai.team'),
        ),
        migrations.CreateModel(
            name='TeamMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(default='member', max_length=50)),
                ('order', models.IntegerField(default=0, help_text='Order in team execution')),
                ('is_selector', models.BooleanField(default=False, help_text='Is this agent a selector in group chat')),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='agenticai.agentprofile')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='agenticai.team')),
            ],
            options={
                'ordering': ['order'],
                'unique_together': {('team', 'agent')},
            },
        ),
        migrations.AddField(
            model_name='team',
            name='agents',
            field=models.ManyToManyField(through='agenticai.TeamMembership', to='agenticai.agentprofile'),
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('user', 'User Message'), ('assistant', 'Assistant Message'), ('system', 'System Message'), ('tool_call', 'Tool Call'), ('tool_result', 'Tool Result')], default='assistant', max_length=20)),
                ('content', models.TextField()),
                ('metadata', models.JSONField(default=dict, help_text='Message metadata (tokens, model used, etc.)')),
                ('sequence_number', models.IntegerField(help_text='Order of message in conversation')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='agenticai.conversation')),
                ('sender_agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='agenticai.agentprofile')),
            ],
            options={
                'ordering': ['sequence_number'],
                'unique_together': {('conversation', 'sequence_number')},
            },
        ),
    ]
