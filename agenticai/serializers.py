"""
Django REST Framework serializers for AgenticAI models
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from .models import AgentProfile, Team, TeamMembership, Conversation, Message, Task


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model"""
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']
        read_only_fields = ['id']


class AgentProfileSerializer(serializers.ModelSerializer):
    """Serializer for AgentProfile model"""
    created_by = UserSerializer(read_only=True)
    
    class Meta:
        model = AgentProfile
        fields = [
            'id', 'name', 'agent_type', 'system_message', 'model_config',
            'tools', 'is_active', 'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']
    
    def validate_model_config(self, value):
        """Validate model configuration"""
        required_fields = ['type', 'model']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"'{field}' is required in model_config")
        
        if value['type'] not in ['openai', 'azure_openai']:
            raise serializers.ValidationError("model type must be 'openai' or 'azure_openai'")
        
        return value


class TeamMembershipSerializer(serializers.ModelSerializer):
    """Serializer for TeamMembership model"""
    agent = AgentProfileSerializer(read_only=True)
    agent_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = TeamMembership
        fields = ['id', 'agent', 'agent_id', 'role', 'order', 'is_selector']


class TeamSerializer(serializers.ModelSerializer):
    """Serializer for Team model"""
    created_by = UserSerializer(read_only=True)
    memberships = TeamMembershipSerializer(source='teammembership_set', many=True, read_only=True)
    agent_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="List of agent IDs to add to the team"
    )
    
    class Meta:
        model = Team
        fields = [
            'id', 'name', 'team_type', 'description', 'termination_config',
            'max_turns', 'is_active', 'created_by', 'created_at', 'updated_at',
            'memberships', 'agent_ids'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        """Create team with agent memberships"""
        agent_ids = validated_data.pop('agent_ids', [])
        team = super().create(validated_data)
        
        # Add agents to team
        for i, agent_id in enumerate(agent_ids):
            try:
                agent = AgentProfile.objects.get(id=agent_id, created_by=self.context['request'].user)
                TeamMembership.objects.create(
                    team=team,
                    agent=agent,
                    order=i
                )
            except AgentProfile.DoesNotExist:
                pass  # Skip invalid agent IDs
        
        return team
    
    def update(self, instance, validated_data):
        """Update team and memberships"""
        agent_ids = validated_data.pop('agent_ids', None)
        team = super().update(instance, validated_data)
        
        if agent_ids is not None:
            # Clear existing memberships
            team.teammembership_set.all().delete()
            
            # Add new memberships
            for i, agent_id in enumerate(agent_ids):
                try:
                    agent = AgentProfile.objects.get(id=agent_id, created_by=self.context['request'].user)
                    TeamMembership.objects.create(
                        team=team,
                        agent=agent,
                        order=i
                    )
                except AgentProfile.DoesNotExist:
                    pass  # Skip invalid agent IDs
        
        return team


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for Message model"""
    sender_agent = AgentProfileSerializer(read_only=True)
    
    class Meta:
        model = Message
        fields = [
            'id', 'conversation', 'sender_agent', 'message_type', 'content',
            'metadata', 'sequence_number', 'timestamp'
        ]
        read_only_fields = ['id', 'timestamp']


class ConversationSerializer(serializers.ModelSerializer):
    """Serializer for Conversation model"""
    started_by = UserSerializer(read_only=True)
    team = TeamSerializer(read_only=True)
    messages_count = serializers.SerializerMethodField()
    duration_seconds = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = [
            'id', 'title', 'team', 'status', 'initial_message', 'final_result',
            'metadata', 'started_by', 'started_at', 'completed_at',
            'messages_count', 'duration_seconds'
        ]
        read_only_fields = ['id', 'started_by', 'started_at', 'completed_at']
    
    def get_messages_count(self, obj):
        """Get the number of messages in the conversation"""
        return obj.messages.count()
    
    def get_duration_seconds(self, obj):
        """Get conversation duration in seconds"""
        duration = obj.duration()
        return duration.total_seconds() if duration else None


class TaskSerializer(serializers.ModelSerializer):
    """Serializer for Task model"""
    created_by = UserSerializer(read_only=True)
    assigned_team = TeamSerializer(read_only=True)
    assigned_agent = AgentProfileSerializer(read_only=True)
    conversation = ConversationSerializer(read_only=True)
    assigned_team_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    assigned_agent_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    class Meta:
        model = Task
        fields = [
            'id', 'title', 'description', 'assigned_team', 'assigned_agent',
            'priority', 'status', 'result', 'conversation', 'created_by',
            'created_at', 'updated_at', 'due_date', 'assigned_team_id',
            'assigned_agent_id'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at', 'result', 'conversation']
    
    def create(self, validated_data):
        """Create task with team/agent assignment"""
        assigned_team_id = validated_data.pop('assigned_team_id', None)
        assigned_agent_id = validated_data.pop('assigned_agent_id', None)
        
        task = super().create(validated_data)
        
        # Assign team if provided
        if assigned_team_id:
            try:
                team = Team.objects.get(id=assigned_team_id, created_by=self.context['request'].user)
                task.assigned_team = team
            except Team.DoesNotExist:
                pass
        
        # Assign agent if provided
        if assigned_agent_id:
            try:
                agent = AgentProfile.objects.get(id=assigned_agent_id, created_by=self.context['request'].user)
                task.assigned_agent = agent
            except AgentProfile.DoesNotExist:
                pass
        
        task.save()
        return task
    
    def update(self, instance, validated_data):
        """Update task with team/agent assignment"""
        assigned_team_id = validated_data.pop('assigned_team_id', None)
        assigned_agent_id = validated_data.pop('assigned_agent_id', None)
        
        task = super().update(instance, validated_data)
        
        # Update team assignment
        if assigned_team_id is not None:
            if assigned_team_id:
                try:
                    team = Team.objects.get(id=assigned_team_id, created_by=self.context['request'].user)
                    task.assigned_team = team
                except Team.DoesNotExist:
                    pass
            else:
                task.assigned_team = None
        
        # Update agent assignment
        if assigned_agent_id is not None:
            if assigned_agent_id:
                try:
                    agent = AgentProfile.objects.get(id=assigned_agent_id, created_by=self.context['request'].user)
                    task.assigned_agent = agent
                except AgentProfile.DoesNotExist:
                    pass
            else:
                task.assigned_agent = None
        
        task.save()
        return task


class ConversationDetailSerializer(ConversationSerializer):
    """Detailed serializer for Conversation with messages"""
    messages = MessageSerializer(many=True, read_only=True)
    
    class Meta(ConversationSerializer.Meta):
        fields = ConversationSerializer.Meta.fields + ['messages']
