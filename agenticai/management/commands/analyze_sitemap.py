"""
Management command to analyze website sitemaps using ultimate-sitemap-parser
"""

from django.core.management.base import BaseCommand
from agenticai.sitemap_service import get_website_sitemap_analysis, extract_urls_from_sitemap, discover_website_sitemaps
import json


class Command(BaseCommand):
    help = 'Analyze website sitemaps and extract URL information'

    def add_arguments(self, parser):
        parser.add_argument(
            'url',
            type=str,
            help='Website URL or direct sitemap URL to analyze'
        )
        parser.add_argument(
            '--max-urls',
            type=int,
            default=50,
            help='Maximum number of URLs to extract (default: 50)'
        )
        parser.add_argument(
            '--sitemap-only',
            action='store_true',
            help='Treat the URL as a direct sitemap URL instead of a website base URL'
        )
        parser.add_argument(
            '--discover-only',
            action='store_true',
            help='Only discover sitemap URLs without extracting content'
        )
        parser.add_argument(
            '--output-json',
            type=str,
            help='Save results to a JSON file'
        )

    def handle(self, *args, **options):
        url = options['url']
        max_urls = options['max_urls']
        
        self.stdout.write(f'🔍 Analyzing: {url}')
        
        try:
            if options['sitemap_only']:
                # Treat as direct sitemap URL
                self.stdout.write('📄 Extracting URLs from sitemap...')
                results = extract_urls_from_sitemap(url, max_urls)
                self.display_sitemap_results(url, results)
                
            elif options['discover_only']:
                # Only discover sitemaps
                self.stdout.write('🔎 Discovering sitemaps...')
                sitemaps = discover_website_sitemaps(url)
                self.display_discovery_results(url, sitemaps)
                results = {'discovered_sitemaps': sitemaps}
                
            else:
                # Full website analysis
                self.stdout.write('🌐 Performing full website analysis...')
                results = get_website_sitemap_analysis(url, max_urls)
                self.display_full_analysis(results)
            
            # Save to JSON file if requested
            if options['output_json']:
                with open(options['output_json'], 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Results saved to {options["output_json"]}')
                )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error analyzing {url}: {str(e)}')
            )

    def display_sitemap_results(self, sitemap_url, urls):
        """Display results from a single sitemap"""
        self.stdout.write(f'\n📊 Sitemap Analysis Results for: {sitemap_url}')
        self.stdout.write(f'Total URLs found: {len(urls)}')
        
        if urls:
            self.stdout.write('\n📋 Sample URLs:')
            for i, url_info in enumerate(urls[:10], 1):
                self.stdout.write(f'  {i}. {url_info["url"]}')
                if url_info['last_modified']:
                    self.stdout.write(f'     Last Modified: {url_info["last_modified"]}')
                if url_info['priority']:
                    self.stdout.write(f'     Priority: {url_info["priority"]}')
                if url_info['change_frequency']:
                    self.stdout.write(f'     Change Frequency: {url_info["change_frequency"]}')
            
            if len(urls) > 10:
                self.stdout.write(f'  ... and {len(urls) - 10} more URLs')

    def display_discovery_results(self, base_url, sitemaps):
        """Display sitemap discovery results"""
        self.stdout.write(f'\n🔎 Sitemap Discovery Results for: {base_url}')
        self.stdout.write(f'Sitemaps found: {len(sitemaps)}')
        
        if sitemaps:
            self.stdout.write('\n📄 Discovered Sitemaps:')
            for i, sitemap_url in enumerate(sitemaps, 1):
                self.stdout.write(f'  {i}. {sitemap_url}')
        else:
            self.stdout.write('❌ No sitemaps found')

    def display_full_analysis(self, analysis):
        """Display full website analysis results"""
        self.stdout.write(f'\n🌐 Website Analysis Results for: {analysis["base_url"]}')
        self.stdout.write(f'Sitemaps found: {analysis["sitemaps_found"]}')
        self.stdout.write(f'Total URLs: {analysis["total_urls"]}')
        
        # Display discovered sitemaps
        if analysis['sitemaps']:
            self.stdout.write('\n📄 Discovered Sitemaps:')
            for i, sitemap_data in enumerate(analysis['sitemaps'], 1):
                self.stdout.write(f'  {i}. {sitemap_data["sitemap_url"]} ({len(sitemap_data["urls"])} URLs)')
        
        # Display URL patterns
        if 'url_patterns' in analysis:
            patterns = analysis['url_patterns']
            self.stdout.write('\n📊 URL Patterns Analysis:')
            
            if patterns['domains']:
                self.stdout.write(f'  Domains: {", ".join(patterns["domains"])}')
            
            if patterns['url_depths']:
                self.stdout.write('  URL Depths:')
                for depth, count in sorted(patterns['url_depths'].items()):
                    self.stdout.write(f'    Depth {depth}: {count} URLs')
            
            if patterns['path_patterns']:
                self.stdout.write('  Top Path Patterns:')
                sorted_patterns = sorted(patterns['path_patterns'].items(), key=lambda x: x[1], reverse=True)
                for pattern, count in sorted_patterns[:10]:
                    self.stdout.write(f'    /{pattern}/: {count} URLs')
            
            if patterns['file_extensions']:
                self.stdout.write('  File Extensions:')
                sorted_extensions = sorted(patterns['file_extensions'].items(), key=lambda x: x[1], reverse=True)
                for ext, count in sorted_extensions[:10]:
                    self.stdout.write(f'    .{ext}: {count} files')
        
        # Display sample URLs
        if analysis['all_urls']:
            self.stdout.write('\n📋 Sample URLs:')
            for i, url_info in enumerate(analysis['all_urls'][:10], 1):
                self.stdout.write(f'  {i}. {url_info["url"]}')
            
            if len(analysis['all_urls']) > 10:
                self.stdout.write(f'  ... and {len(analysis["all_urls"]) - 10} more URLs')
        
        self.stdout.write(
            self.style.SUCCESS(f'\n✅ Analysis complete!')
        )
