"""
Management command to create sample data for testing AutoGen 0.6.2 integration
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from agenticai.models import AgentProfile, Team, TeamMembership


class Command(BaseCommand):
    help = 'Create sample agents and teams for testing AutoGen 0.6.2 integration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=str,
            help='Username to assign the sample data to (default: admin)',
            default='admin'
        )

    def handle(self, *args, **options):
        username = options['user']
        
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User "{username}" does not exist. Please create a superuser first.')
            )
            return

        # Create sample agents
        self.stdout.write('Creating sample agents...')
        
        # Research Assistant Agent
        research_agent, created = AgentProfile.objects.get_or_create(
            name='Research Assistant',
            defaults={
                'agent_type': 'assistant',
                'system_message': 'You are a helpful research assistant. You excel at finding information, analyzing data, and providing comprehensive research summaries.',
                'model_config': {
                    'type': 'openai',
                    'model': 'gpt-4'
                },
                'tools': [],
                'created_by': user
            }
        )
        if created:
            self.stdout.write(f'  ✓ Created Research Assistant agent')
        else:
            self.stdout.write(f'  - Research Assistant agent already exists')

        # Code Assistant Agent
        code_agent, created = AgentProfile.objects.get_or_create(
            name='Code Assistant',
            defaults={
                'agent_type': 'assistant',
                'system_message': 'You are an expert software developer. You can write, review, and debug code in multiple programming languages.',
                'model_config': {
                    'type': 'openai',
                    'model': 'gpt-4'
                },
                'tools': [],
                'created_by': user
            }
        )
        if created:
            self.stdout.write(f'  ✓ Created Code Assistant agent')
        else:
            self.stdout.write(f'  - Code Assistant agent already exists')

        # User Proxy Agent
        user_proxy, created = AgentProfile.objects.get_or_create(
            name='User Proxy',
            defaults={
                'agent_type': 'user_proxy',
                'system_message': 'You are a user proxy agent that facilitates communication between the user and other agents.',
                'model_config': {
                    'type': 'openai',
                    'model': 'gpt-4'
                },
                'tools': [],
                'created_by': user
            }
        )
        if created:
            self.stdout.write(f'  ✓ Created User Proxy agent')
        else:
            self.stdout.write(f'  - User Proxy agent already exists')

        # Project Manager Agent
        pm_agent, created = AgentProfile.objects.get_or_create(
            name='Project Manager',
            defaults={
                'agent_type': 'assistant',
                'system_message': 'You are a project manager who coordinates tasks, manages timelines, and ensures project deliverables are met.',
                'model_config': {
                    'type': 'openai',
                    'model': 'gpt-4'
                },
                'tools': [],
                'created_by': user
            }
        )
        if created:
            self.stdout.write(f'  ✓ Created Project Manager agent')
        else:
            self.stdout.write(f'  - Project Manager agent already exists')

        # Create sample teams
        self.stdout.write('\nCreating sample teams...')

        # Research Team
        research_team, created = Team.objects.get_or_create(
            name='Research Team',
            defaults={
                'team_type': 'round_robin',
                'description': 'A team specialized in research and analysis tasks',
                'termination_config': {
                    'max_turns': 10
                },
                'max_turns': 10,
                'created_by': user
            }
        )
        if created:
            self.stdout.write(f'  ✓ Created Research Team')
            # Add agents to research team
            TeamMembership.objects.create(team=research_team, agent=research_agent, order=0, role='researcher')
            TeamMembership.objects.create(team=research_team, agent=user_proxy, order=1, role='coordinator')
        else:
            self.stdout.write(f'  - Research Team already exists')

        # Development Team
        dev_team, created = Team.objects.get_or_create(
            name='Development Team',
            defaults={
                'team_type': 'selector_group_chat',
                'description': 'A team for software development and coding tasks',
                'termination_config': {
                    'max_turns': 15
                },
                'max_turns': 15,
                'created_by': user
            }
        )
        if created:
            self.stdout.write(f'  ✓ Created Development Team')
            # Add agents to development team
            TeamMembership.objects.create(team=dev_team, agent=code_agent, order=0, role='developer')
            TeamMembership.objects.create(team=dev_team, agent=pm_agent, order=1, role='manager', is_selector=True)
            TeamMembership.objects.create(team=dev_team, agent=user_proxy, order=2, role='coordinator')
        else:
            self.stdout.write(f'  - Development Team already exists')

        # Full Project Team
        full_team, created = Team.objects.get_or_create(
            name='Full Project Team',
            defaults={
                'team_type': 'round_robin',
                'description': 'A comprehensive team with all agent types for complex projects',
                'termination_config': {
                    'max_turns': 20
                },
                'max_turns': 20,
                'created_by': user
            }
        )
        if created:
            self.stdout.write(f'  ✓ Created Full Project Team')
            # Add all agents to full team
            TeamMembership.objects.create(team=full_team, agent=pm_agent, order=0, role='manager')
            TeamMembership.objects.create(team=full_team, agent=research_agent, order=1, role='researcher')
            TeamMembership.objects.create(team=full_team, agent=code_agent, order=2, role='developer')
            TeamMembership.objects.create(team=full_team, agent=user_proxy, order=3, role='coordinator')
        else:
            self.stdout.write(f'  - Full Project Team already exists')

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Sample data creation completed!\n'
                f'Created agents and teams for user: {username}\n'
                f'You can now test the AutoGen 0.6.2 integration using the Django admin or API.'
            )
        )
