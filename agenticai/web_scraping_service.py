"""
Web Scraping Service using ultimate-sitemap-parser
This service provides web scraping capabilities for AutoGen agents
"""

import logging
import requests
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from ultimate_sitemap_parser import SitemapParser
from bs4 import BeautifulSoup
import time
from django.conf import settings

logger = logging.getLogger(__name__)


class WebScrapingService:
    """Service for web scraping and sitemap parsing"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.rate_limit_delay = 1  # seconds between requests
    
    def parse_sitemap(self, sitemap_url: str, max_urls: int = 100) -> List[Dict[str, Any]]:
        """
        Parse a sitemap and return list of URLs with metadata
        
        Args:
            sitemap_url: URL of the sitemap
            max_urls: Maximum number of URLs to return
            
        Returns:
            List of dictionaries containing URL information
        """
        try:
            logger.info(f"Parsing sitemap: {sitemap_url}")
            
            # Parse the sitemap
            parser = SitemapParser(sitemap_url)
            urls = parser.get_urls()
            
            # Limit the number of URLs
            if max_urls and len(urls) > max_urls:
                urls = urls[:max_urls]
            
            # Convert to list of dictionaries with metadata
            result = []
            for url_info in urls:
                url_data = {
                    'url': url_info.url,
                    'last_modified': url_info.last_modified.isoformat() if url_info.last_modified else None,
                    'change_frequency': url_info.change_frequency,
                    'priority': url_info.priority
                }
                result.append(url_data)
            
            logger.info(f"Successfully parsed {len(result)} URLs from sitemap")
            return result
            
        except Exception as e:
            logger.error(f"Error parsing sitemap {sitemap_url}: {str(e)}")
            raise
    
    def scrape_page_content(self, url: str, extract_text_only: bool = True) -> Dict[str, Any]:
        """
        Scrape content from a single web page
        
        Args:
            url: URL to scrape
            extract_text_only: If True, extract only text content
            
        Returns:
            Dictionary containing page content and metadata
        """
        try:
            logger.info(f"Scraping page: {url}")
            
            # Add rate limiting
            time.sleep(self.rate_limit_delay)
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Parse HTML content
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract metadata
            title = soup.find('title')
            title_text = title.get_text().strip() if title else ""
            
            # Extract meta description
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            description = meta_desc.get('content', '') if meta_desc else ""
            
            # Extract main content
            if extract_text_only:
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Get text content
                content = soup.get_text()
                # Clean up whitespace
                lines = (line.strip() for line in content.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                content = ' '.join(chunk for chunk in chunks if chunk)
            else:
                content = str(soup)
            
            result = {
                'url': url,
                'title': title_text,
                'description': description,
                'content': content,
                'content_length': len(content),
                'status_code': response.status_code,
                'scraped_at': time.time()
            }
            
            logger.info(f"Successfully scraped page: {url} ({len(content)} characters)")
            return result
            
        except Exception as e:
            logger.error(f"Error scraping page {url}: {str(e)}")
            raise
    
    def scrape_multiple_pages(self, urls: List[str], max_pages: int = 10) -> List[Dict[str, Any]]:
        """
        Scrape content from multiple web pages
        
        Args:
            urls: List of URLs to scrape
            max_pages: Maximum number of pages to scrape
            
        Returns:
            List of dictionaries containing page content
        """
        results = []
        
        # Limit the number of pages
        if max_pages and len(urls) > max_pages:
            urls = urls[:max_pages]
        
        for url in urls:
            try:
                page_data = self.scrape_page_content(url)
                results.append(page_data)
            except Exception as e:
                logger.warning(f"Failed to scrape {url}: {str(e)}")
                # Continue with other URLs
                continue
        
        return results
    
    def discover_and_scrape_website(self, base_url: str, max_pages: int = 20) -> Dict[str, Any]:
        """
        Discover pages from a website's sitemap and scrape content
        
        Args:
            base_url: Base URL of the website
            max_pages: Maximum number of pages to scrape
            
        Returns:
            Dictionary containing sitemap info and scraped content
        """
        try:
            # Try common sitemap locations
            sitemap_urls = [
                urljoin(base_url, '/sitemap.xml'),
                urljoin(base_url, '/sitemap_index.xml'),
                urljoin(base_url, '/sitemaps.xml'),
                urljoin(base_url, '/robots.txt')  # Check robots.txt for sitemap
            ]
            
            sitemap_data = None
            for sitemap_url in sitemap_urls:
                try:
                    if sitemap_url.endswith('robots.txt'):
                        # Parse robots.txt to find sitemap
                        response = self.session.get(sitemap_url)
                        if response.status_code == 200:
                            for line in response.text.split('\n'):
                                if line.lower().startswith('sitemap:'):
                                    actual_sitemap_url = line.split(':', 1)[1].strip()
                                    sitemap_data = self.parse_sitemap(actual_sitemap_url, max_pages)
                                    break
                    else:
                        sitemap_data = self.parse_sitemap(sitemap_url, max_pages)
                    
                    if sitemap_data:
                        break
                        
                except Exception as e:
                    logger.debug(f"Sitemap not found at {sitemap_url}: {str(e)}")
                    continue
            
            if not sitemap_data:
                logger.warning(f"No sitemap found for {base_url}")
                return {
                    'base_url': base_url,
                    'sitemap_found': False,
                    'pages_scraped': 0,
                    'content': []
                }
            
            # Extract URLs from sitemap data
            urls = [item['url'] for item in sitemap_data]
            
            # Scrape the pages
            scraped_content = self.scrape_multiple_pages(urls, max_pages)
            
            result = {
                'base_url': base_url,
                'sitemap_found': True,
                'sitemap_urls_count': len(sitemap_data),
                'pages_scraped': len(scraped_content),
                'sitemap_data': sitemap_data,
                'content': scraped_content
            }
            
            logger.info(f"Successfully scraped {len(scraped_content)} pages from {base_url}")
            return result
            
        except Exception as e:
            logger.error(f"Error discovering and scraping website {base_url}: {str(e)}")
            raise


# Global service instance
web_scraping_service = WebScrapingService()


# AutoGen tool functions
async def parse_website_sitemap(sitemap_url: str, max_urls: int = 50) -> str:
    """
    AutoGen tool function to parse a website sitemap
    
    Args:
        sitemap_url: URL of the sitemap to parse
        max_urls: Maximum number of URLs to return (default: 50)
        
    Returns:
        String containing sitemap information
    """
    try:
        sitemap_data = web_scraping_service.parse_sitemap(sitemap_url, max_urls)
        
        result = f"Sitemap Analysis for {sitemap_url}:\n"
        result += f"Total URLs found: {len(sitemap_data)}\n\n"
        
        for i, url_info in enumerate(sitemap_data[:10], 1):  # Show first 10
            result += f"{i}. {url_info['url']}\n"
            if url_info['last_modified']:
                result += f"   Last Modified: {url_info['last_modified']}\n"
            if url_info['priority']:
                result += f"   Priority: {url_info['priority']}\n"
            result += "\n"
        
        if len(sitemap_data) > 10:
            result += f"... and {len(sitemap_data) - 10} more URLs\n"
        
        return result
        
    except Exception as e:
        return f"Error parsing sitemap: {str(e)}"


async def scrape_webpage_content(url: str) -> str:
    """
    AutoGen tool function to scrape content from a webpage
    
    Args:
        url: URL of the webpage to scrape
        
    Returns:
        String containing webpage content
    """
    try:
        page_data = web_scraping_service.scrape_page_content(url)
        
        result = f"Webpage Content for {url}:\n"
        result += f"Title: {page_data['title']}\n"
        result += f"Description: {page_data['description']}\n"
        result += f"Content Length: {page_data['content_length']} characters\n\n"
        
        # Truncate content if too long
        content = page_data['content']
        if len(content) > 2000:
            content = content[:2000] + "... [content truncated]"
        
        result += f"Content:\n{content}"
        
        return result
        
    except Exception as e:
        return f"Error scraping webpage: {str(e)}"


async def discover_website_content(base_url: str, max_pages: int = 10) -> str:
    """
    AutoGen tool function to discover and scrape content from a website
    
    Args:
        base_url: Base URL of the website
        max_pages: Maximum number of pages to scrape (default: 10)
        
    Returns:
        String containing website analysis
    """
    try:
        website_data = web_scraping_service.discover_and_scrape_website(base_url, max_pages)
        
        result = f"Website Analysis for {base_url}:\n"
        result += f"Sitemap Found: {website_data['sitemap_found']}\n"
        result += f"Pages Scraped: {website_data['pages_scraped']}\n\n"
        
        if website_data['sitemap_found']:
            result += f"Total URLs in sitemap: {website_data['sitemap_urls_count']}\n\n"
            
            # Show summary of scraped pages
            for i, page in enumerate(website_data['content'][:5], 1):  # Show first 5
                result += f"{i}. {page['title']} ({page['url']})\n"
                result += f"   Description: {page['description'][:100]}...\n"
                result += f"   Content Length: {page['content_length']} characters\n\n"
        
        return result
        
    except Exception as e:
        return f"Error analyzing website: {str(e)}"
