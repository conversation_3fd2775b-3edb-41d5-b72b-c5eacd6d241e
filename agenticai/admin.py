from django.contrib import admin
from .models import <PERSON><PERSON><PERSON><PERSON><PERSON>, Team, TeamMembership, Conversation, Message, Task


@admin.register(AgentProfile)
class AgentProfileAdmin(admin.ModelAdmin):
    list_display = ['name', 'agent_type',
                    'is_active', 'created_by', 'created_at']
    list_filter = ['agent_type', 'is_active', 'created_at']
    search_fields = ['name', 'system_message']
    readonly_fields = ['created_at', 'updated_at']


class TeamMembershipInline(admin.TabularInline):
    model = TeamMembership
    extra = 1


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    list_display = ['name', 'team_type',
                    'is_active', 'created_by', 'created_at']
    list_filter = ['team_type', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [TeamMembershipInline]


class MessageInline(admin.TabularInline):
    model = Message
    extra = 0
    readonly_fields = ['timestamp']


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ['title', 'team', 'status', 'started_by', 'started_at']
    list_filter = ['status', 'started_at']
    search_fields = ['title', 'initial_message']
    readonly_fields = ['started_at', 'completed_at']
    inlines = [MessageInline]


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ['conversation', 'sender_agent',
                    'message_type', 'sequence_number', 'timestamp']
    list_filter = ['message_type', 'timestamp']
    search_fields = ['content']
    readonly_fields = ['timestamp']


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    list_display = ['title', 'assigned_team', 'assigned_agent',
                    'priority', 'status', 'created_by', 'created_at']
    list_filter = ['priority', 'status', 'created_at']
    search_fields = ['title', 'description']
    readonly_fields = ['created_at', 'updated_at']
