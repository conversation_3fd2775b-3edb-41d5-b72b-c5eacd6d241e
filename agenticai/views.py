from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
import asyncio

from .models import AgentProfile, Team, Conversation, Message, Task
from .serializers import (
    AgentProfileSerializer, TeamSerializer, ConversationSerializer,
    MessageSerializer, TaskSerializer
)
from .autogen_service import autogen_service


class AgentProfileViewSet(viewsets.ModelViewSet):
    """ViewSet for managing agent profiles"""
    queryset = AgentProfile.objects.all()
    serializer_class = AgentProfileSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    def get_queryset(self):
        return AgentProfile.objects.filter(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def test_agent(self, request, pk=None):
        """Test an agent configuration"""
        agent_profile = self.get_object()
        test_message = request.data.get(
            'message', 'Hello, please introduce yourself.')

        try:
            # Create a test agent
            agent = autogen_service.create_agent(agent_profile)

            # This would require implementing a simple test conversation
            # For now, return a success response
            return Response({
                'success': True,
                'message': f'Agent {agent_profile.name} configuration is valid',
                'agent_type': agent_profile.agent_type
            })
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class TeamViewSet(viewsets.ModelViewSet):
    """ViewSet for managing teams"""
    queryset = Team.objects.all()
    serializer_class = TeamSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    def get_queryset(self):
        return Team.objects.filter(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def start_conversation(self, request, pk=None):
        """Start a new conversation with this team"""
        team = self.get_object()
        initial_message = request.data.get('message', '')
        title = request.data.get('title', f'Conversation with {team.name}')

        if not initial_message:
            return Response({
                'error': 'Message is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create conversation
        conversation = Conversation.objects.create(
            title=title,
            team=team,
            initial_message=initial_message,
            started_by=request.user
        )

        # Run conversation asynchronously
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            messages = loop.run_until_complete(
                autogen_service.run_conversation(conversation, initial_message)
            )
            loop.close()

            return Response({
                'conversation_id': conversation.id,
                'status': conversation.status,
                'messages_count': len(messages)
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConversationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing conversations"""
    queryset = Conversation.objects.all()
    serializer_class = ConversationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Conversation.objects.filter(started_by=self.request.user)

    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """Get all messages for a conversation"""
        conversation = self.get_object()
        messages = conversation.messages.all()
        serializer = MessageSerializer(messages, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def continue_conversation(self, request, pk=None):
        """Continue an existing conversation"""
        conversation = self.get_object()
        message = request.data.get('message', '')

        if not message:
            return Response({
                'error': 'Message is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if conversation.status != 'active':
            return Response({
                'error': 'Conversation is not active'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            messages = loop.run_until_complete(
                autogen_service.run_conversation(conversation, message)
            )
            loop.close()

            return Response({
                'status': conversation.status,
                'new_messages_count': len(messages)
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TaskViewSet(viewsets.ModelViewSet):
    """ViewSet for managing tasks"""
    queryset = Task.objects.all()
    serializer_class = TaskSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    def get_queryset(self):
        return Task.objects.filter(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute a task using AutoGen"""
        task = self.get_object()

        if task.status != 'pending':
            return Response({
                'error': 'Task is not in pending status'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(
                autogen_service.execute_task(task)
            )
            loop.close()

            return Response(result)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def by_status(self, request):
        """Get tasks filtered by status"""
        status_filter = request.query_params.get('status', 'pending')
        tasks = self.get_queryset().filter(status=status_filter)
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
