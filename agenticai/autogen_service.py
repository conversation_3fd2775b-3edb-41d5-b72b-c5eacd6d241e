"""
AutoGen 0.6.2 Service Layer
This module provides integration with AutoGen 0.6.2 AgentChat API
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from django.conf import settings

# AutoGen 0.6.2 imports
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
from autogen_ext.models.openai import OpenAIChatCompletionClient, AzureOpenAIChatCompletionClient
from autogen_agentchat.messages import TextMessage, FunctionCall, FunctionExecutionResult
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination

from .models import AgentProfile, Team, Conversation, Message, Task
from .web_scraping_service import parse_website_sitemap, scrape_webpage_content, discover_website_content

logger = logging.getLogger(__name__)


class AutoGenService:
    """Service class for AutoGen 0.6.2 integration"""

    def __init__(self):
        self.config = settings.AUTOGEN_CONFIG
        self._model_clients = {}

    def get_model_client(self, model_config: Dict[str, Any]):
        """Get or create a model client based on configuration"""
        model_type = model_config.get('type', 'openai')
        model_name = model_config.get('model', self.config['DEFAULT_MODEL'])

        cache_key = f"{model_type}_{model_name}"
        if cache_key in self._model_clients:
            return self._model_clients[cache_key]

        if model_type == 'openai':
            client = OpenAIChatCompletionClient(
                model=model_name,
                api_key=model_config.get(
                    'api_key') or self.config['OPENAI_API_KEY']
            )
        elif model_type == 'azure_openai':
            client = AzureOpenAIChatCompletionClient(
                model=model_name,
                api_key=model_config.get(
                    'api_key') or self.config['AZURE_OPENAI_API_KEY'],
                azure_endpoint=model_config.get(
                    'endpoint') or self.config['AZURE_OPENAI_ENDPOINT']
            )
        else:
            raise ValueError(f"Unsupported model type: {model_type}")

        self._model_clients[cache_key] = client
        return client

    def create_agent(self, agent_profile: AgentProfile):
        """Create an AutoGen agent from AgentProfile"""
        model_client = self.get_model_client(agent_profile.model_config)

        if agent_profile.agent_type == 'assistant':
            return AssistantAgent(
                name=agent_profile.name,
                model_client=model_client,
                system_message=agent_profile.system_message,
                tools=agent_profile.tools or []
            )
        elif agent_profile.agent_type == 'user_proxy':
            return UserProxyAgent(
                name=agent_profile.name,
                system_message=agent_profile.system_message
            )
        else:
            # For custom agents, default to AssistantAgent
            return AssistantAgent(
                name=agent_profile.name,
                model_client=model_client,
                system_message=agent_profile.system_message,
                tools=agent_profile.tools or []
            )

    def create_team(self, team: Team):
        """Create an AutoGen team from Team model"""
        # Get team members in order
        memberships = team.teammembership_set.all().order_by('order')
        agents = []

        for membership in memberships:
            agent = self.create_agent(membership.agent)
            agents.append(agent)

        # Create termination condition
        termination_condition = MaxMessageTermination(team.max_turns)
        if team.termination_config.get('text_mention'):
            termination_condition = TextMentionTermination(
                team.termination_config['text_mention']
            )

        if team.team_type == 'round_robin':
            return RoundRobinGroupChat(
                participants=agents,
                termination_condition=termination_condition
            )
        elif team.team_type == 'selector_group_chat':
            # Find selector agent
            selector_membership = memberships.filter(is_selector=True).first()
            if selector_membership:
                selector_agent = self.create_agent(selector_membership.agent)
                return SelectorGroupChat(
                    participants=agents,
                    model_client=self.get_model_client(
                        selector_membership.agent.model_config),
                    termination_condition=termination_condition
                )

        # Default to RoundRobinGroupChat
        return RoundRobinGroupChat(
            participants=agents,
            termination_condition=termination_condition
        )

    async def run_conversation(self, conversation: Conversation, message: str) -> List[Dict]:
        """Run a conversation with AutoGen team"""
        try:
            # Create team
            team = self.create_team(conversation.team)

            # Create initial message
            initial_message = TextMessage(content=message, source="user")

            # Run the conversation
            result = await team.run(task=initial_message)

            # Process and save messages
            messages_data = []
            for i, msg in enumerate(result.messages):
                message_data = {
                    'conversation': conversation,
                    'content': msg.content if hasattr(msg, 'content') else str(msg),
                    'message_type': self._get_message_type(msg),
                    'sequence_number': i,
                    'metadata': {
                        'source': getattr(msg, 'source', 'unknown'),
                        'model_used': getattr(msg, 'model_used', None)
                    }
                }

                # Find sender agent
                if hasattr(msg, 'source') and msg.source != 'user':
                    try:
                        sender_agent = AgentProfile.objects.get(
                            name=msg.source)
                        message_data['sender_agent'] = sender_agent
                    except AgentProfile.DoesNotExist:
                        pass

                messages_data.append(message_data)

                # Save to database
                Message.objects.create(**message_data)

            # Update conversation status
            conversation.status = 'completed'
            conversation.final_result = result.messages[-1].content if result.messages else ""
            conversation.save()

            return messages_data

        except Exception as e:
            logger.error(
                f"Error running conversation {conversation.id}: {str(e)}")
            conversation.status = 'failed'
            conversation.final_result = f"Error: {str(e)}"
            conversation.save()
            raise

    def _get_message_type(self, message) -> str:
        """Determine message type from AutoGen message"""
        if isinstance(message, TextMessage):
            return 'assistant' if hasattr(message, 'source') and message.source != 'user' else 'user'
        elif isinstance(message, FunctionCall):
            return 'tool_call'
        elif isinstance(message, FunctionExecutionResult):
            return 'tool_result'
        else:
            return 'system'

    async def execute_task(self, task: Task) -> Dict[str, Any]:
        """Execute a task using AutoGen agents"""
        try:
            task.status = 'in_progress'
            task.save()

            if task.assigned_team:
                # Create conversation for the task
                conversation = Conversation.objects.create(
                    title=f"Task: {task.title}",
                    team=task.assigned_team,
                    initial_message=task.description,
                    started_by=task.created_by
                )
                task.conversation = conversation
                task.save()

                # Run the conversation
                messages = await self.run_conversation(conversation, task.description)

                # Update task with result
                task.status = 'completed'
                task.result = conversation.final_result
                task.save()

                return {
                    'success': True,
                    'result': task.result,
                    'conversation_id': conversation.id,
                    'messages': messages
                }
            else:
                raise ValueError("Task must have an assigned team")

        except Exception as e:
            task.status = 'failed'
            task.result = f"Error: {str(e)}"
            task.save()
            logger.error(f"Error executing task {task.id}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


# Global service instance
autogen_service = AutoGenService()
