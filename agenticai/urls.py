"""
URL configuration for AgenticAI app
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'agents', views.AgentProfileViewSet, basename='agentprofile')
router.register(r'teams', views.TeamViewSet, basename='team')
router.register(r'conversations', views.ConversationViewSet,
                basename='conversation')
router.register(r'tasks', views.TaskViewSet, basename='task')
router.register(r'sitemap', views.SitemapViewSet, basename='sitemap')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('api/', include(router.urls)),
]
