# --------------------------------------------------------------------------
#
# Copyright (c) Microsoft Corporation. All rights reserved.
#
# The MIT License (MIT)
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the ""Software""), to
# deal in the Software without restriction, including without limitation the
# rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
# sell copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED *AS IS*, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
# FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
# IN THE SOFTWARE.
#
# --------------------------------------------------------------------------

from ._index import (
    ComplexField,
    SearchField,
    SearchableField,
    SimpleField,
    SearchIndex,
)
from . import _edm
from ..._generated.models import SuggestOptions
from .._generated.models import (
    AnalyzeResult,
    AnalyzedTokenInfo,
    AsciiFoldingTokenFilter,
    AzureOpenAIEmbeddingSkill,
    AzureOpenAIModelName,
    AzureOpenAIVectorizer,
    AzureOpenAIVectorizerParameters,
    BinaryQuantizationCompression,
    BlobIndexerDataToExtract,
    BlobIndexerImageAction,
    BlobIndexerParsingMode,
    BlobIndexerPDFTextRotationAlgorithm,
    BM25SimilarityAlgorithm,
    CharFilter,
    CharFilterName,
    CjkBigramTokenFilter,
    CjkBigramTokenFilterScripts,
    ClassicSimilarityAlgorithm,
    ClassicTokenizer,
    CognitiveServicesAccount,
    CognitiveServicesAccountKey,
    CommonGramTokenFilter,
    ConditionalSkill,
    CorsOptions,
    CustomEntity,
    CustomEntityAlias,
    CustomEntityLookupSkill,
    CustomEntityLookupSkillLanguage,
    DataChangeDetectionPolicy,
    DataDeletionDetectionPolicy,
    DefaultCognitiveServicesAccount,
    DictionaryDecompounderTokenFilter,
    DistanceScoringFunction,
    DistanceScoringParameters,
    DocumentExtractionSkill,
    EdgeNGramTokenFilter,
    EdgeNGramTokenizer,
    EdgeNGramTokenFilterSide,
    ElisionTokenFilter,
    EntityCategory,
    EntityLinkingSkill,
    EntityRecognitionSkillLanguage,
    ExhaustiveKnnAlgorithmConfiguration,
    ExhaustiveKnnParameters,
    FieldMapping,
    FieldMappingFunction,
    FreshnessScoringFunction,
    FreshnessScoringParameters,
    GetIndexStatisticsResult,
    HighWaterMarkChangeDetectionPolicy,
    HnswParameters,
    HnswAlgorithmConfiguration,
    ImageAnalysisSkill,
    ImageAnalysisSkillLanguage,
    ImageDetail,
    IndexerExecutionEnvironment,
    IndexerExecutionResult,
    IndexerExecutionStatus,
    IndexProjectionMode,
    IndexerStatus,
    IndexingParameters,
    IndexingParametersConfiguration,
    IndexingSchedule,
    InputFieldMappingEntry,
    KeepTokenFilter,
    KeyPhraseExtractionSkill,
    KeyPhraseExtractionSkillLanguage,
    KeywordMarkerTokenFilter,
    KeywordTokenizerV2,
    LanguageDetectionSkill,
    LengthTokenFilter,
    LexicalAnalyzer,
    LexicalAnalyzerName,
    LexicalTokenizer,
    LexicalTokenizerName,
    LimitTokenFilter,
    LuceneStandardAnalyzer,
    LuceneStandardTokenizer,
    MagnitudeScoringFunction,
    MagnitudeScoringParameters,
    MappingCharFilter,
    MergeSkill,
    MicrosoftLanguageStemmingTokenizer,
    MicrosoftLanguageTokenizer,
    MicrosoftStemmingTokenizerLanguage,
    MicrosoftTokenizerLanguage,
    NGramTokenFilter,
    NGramTokenizer,
    OcrLineEnding,
    OcrSkill,
    OcrSkillLanguage,
    OutputFieldMappingEntry,
    PathHierarchyTokenizerV2,
    PatternCaptureTokenFilter,
    PatternReplaceCharFilter,
    PatternReplaceTokenFilter,
    PhoneticEncoder,
    PhoneticTokenFilter,
    PIIDetectionSkill,
    PIIDetectionSkillMaskingMode,
    RegexFlags,
    ScalarQuantizationCompression,
    ScalarQuantizationParameters,
    ScoringFunction,
    ScoringFunctionAggregation,
    ScoringFunctionInterpolation,
    ScoringProfile,
    SearchIndexerDataContainer,
    SearchIndexerDataIdentity,
    SearchIndexerDataNoneIdentity,
    SearchIndexerDataUserAssignedIdentity,
    SearchIndexerDataSourceType,
    SearchIndexerError,
    SearchIndexerIndexProjection,
    SearchIndexerIndexProjectionSelector,
    SearchIndexerIndexProjectionsParameters,
    SearchIndexerKnowledgeStore,
    SearchIndexerKnowledgeStoreBlobProjectionSelector,
    SearchIndexerKnowledgeStoreFileProjectionSelector,
    SearchIndexerKnowledgeStoreObjectProjectionSelector,
    SearchIndexerKnowledgeStoreProjection,
    SearchIndexerKnowledgeStoreProjectionSelector,
    SearchIndexerKnowledgeStoreTableProjectionSelector,
    SearchIndexerLimits,
    SearchIndexerSkill,
    SearchIndexerStatus,
    SearchIndexerWarning,
    SearchServiceCounters,
    SearchServiceLimits,
    SearchServiceStatistics,
    SearchSuggester,
    SemanticConfiguration,
    SemanticField,
    SemanticPrioritizedFields,
    SemanticSearch,
    SentimentSkillLanguage,
    ShaperSkill,
    ShingleTokenFilter,
    SimilarityAlgorithm,
    SnowballTokenFilter,
    SnowballTokenFilterLanguage,
    SoftDeleteColumnDeletionDetectionPolicy,
    SplitSkill,
    SplitSkillLanguage,
    SqlIntegratedChangeTrackingPolicy,
    StemmerOverrideTokenFilter,
    StemmerTokenFilter,
    StemmerTokenFilterLanguage,
    StopAnalyzer,
    StopwordsList,
    StopwordsTokenFilter,
    SynonymTokenFilter,
    TagScoringFunction,
    TagScoringParameters,
    TextSplitMode,
    TextTranslationSkill,
    TextTranslationSkillLanguage,
    TextWeights,
    TokenCharacterKind,
    TokenFilter,
    TokenFilterName,
    TruncateTokenFilter,
    UaxUrlEmailTokenizer,
    UniqueTokenFilter,
    VectorEncodingFormat,
    VectorSearch,
    VectorSearchAlgorithmConfiguration,
    VectorSearchAlgorithmKind,
    VectorSearchAlgorithmMetric,
    VectorSearchCompression,
    VectorSearchCompressionTarget,
    VectorSearchProfile,
    VectorSearchVectorizer,
    VectorSearchVectorizerKind,
    VisualFeature,
    WebApiSkill,
    WebApiVectorizer,
    WebApiVectorizerParameters,
    WordDelimiterTokenFilter,
)
from ._models import (
    AnalyzeTextOptions,
    CustomAnalyzer,
    EntityRecognitionSkill,
    EntityRecognitionSkillVersion,
    PatternAnalyzer,
    PatternTokenizer,
    SearchIndexer,
    SearchIndexerDataSourceConnection,
    SearchIndexerSkillset,
    SearchResourceEncryptionKey,
    SentimentSkill,
    SentimentSkillVersion,
    SynonymMap,
)

SearchFieldDataType = _edm


class KeywordTokenizer(KeywordTokenizerV2):
    pass


class PathHierarchyTokenizer(PathHierarchyTokenizerV2):
    pass


__all__ = (
    "AnalyzeTextOptions",
    "AnalyzeResult",
    "AnalyzedTokenInfo",
    "AsciiFoldingTokenFilter",
    "AzureOpenAIEmbeddingSkill",
    "AzureOpenAIModelName",
    "AzureOpenAIVectorizer",
    "AzureOpenAIVectorizerParameters",
    "BinaryQuantizationCompression",
    "BlobIndexerDataToExtract",
    "BlobIndexerImageAction",
    "BlobIndexerParsingMode",
    "BlobIndexerPDFTextRotationAlgorithm",
    "BM25SimilarityAlgorithm",
    "CharFilter",
    "CharFilterName",
    "CjkBigramTokenFilter",
    "CjkBigramTokenFilterScripts",
    "ClassicSimilarityAlgorithm",
    "ClassicTokenizer",
    "CognitiveServicesAccount",
    "CognitiveServicesAccountKey",
    "CommonGramTokenFilter",
    "ComplexField",
    "ConditionalSkill",
    "CorsOptions",
    "CustomAnalyzer",
    "CustomEntity",
    "CustomEntityAlias",
    "CustomEntityLookupSkill",
    "DefaultCognitiveServicesAccount",
    "CustomEntityLookupSkillLanguage",
    "DataChangeDetectionPolicy",
    "DataDeletionDetectionPolicy",
    "DefaultCognitiveServicesAccount",
    "DictionaryDecompounderTokenFilter",
    "DistanceScoringFunction",
    "DistanceScoringParameters",
    "DocumentExtractionSkill",
    "EdgeNGramTokenFilter",
    "EdgeNGramTokenizer",
    "ElisionTokenFilter",
    "EdgeNGramTokenFilterSide",
    "EntityCategory",
    "EntityLinkingSkill",
    "EntityRecognitionSkill",
    "EntityRecognitionSkillLanguage",
    "EntityRecognitionSkillVersion",
    "ExhaustiveKnnAlgorithmConfiguration",
    "ExhaustiveKnnParameters",
    "FieldMapping",
    "FieldMappingFunction",
    "FreshnessScoringFunction",
    "FreshnessScoringParameters",
    "GetIndexStatisticsResult",
    "HighWaterMarkChangeDetectionPolicy",
    "HnswParameters",
    "HnswAlgorithmConfiguration",
    "ImageAnalysisSkill",
    "ImageAnalysisSkillLanguage",
    "ImageDetail",
    "IndexerExecutionEnvironment",
    "IndexerExecutionResult",
    "IndexerExecutionStatus",
    "IndexProjectionMode",
    "IndexerStatus",
    "IndexingParameters",
    "IndexingParametersConfiguration",
    "IndexingSchedule",
    "InputFieldMappingEntry",
    "KeepTokenFilter",
    "KeyPhraseExtractionSkill",
    "KeyPhraseExtractionSkillLanguage",
    "KeywordMarkerTokenFilter",
    "KeywordTokenizer",
    "LanguageDetectionSkill",
    "LengthTokenFilter",
    "LexicalAnalyzer",
    "LexicalAnalyzerName",
    "LexicalTokenizer",
    "LexicalTokenizerName",
    "LimitTokenFilter",
    "LuceneStandardAnalyzer",
    "LuceneStandardTokenizer",
    "MagnitudeScoringFunction",
    "MagnitudeScoringParameters",
    "MappingCharFilter",
    "MergeSkill",
    "MicrosoftLanguageStemmingTokenizer",
    "MicrosoftLanguageTokenizer",
    "MicrosoftStemmingTokenizerLanguage",
    "MicrosoftTokenizerLanguage",
    "NGramTokenFilter",
    "NGramTokenizer",
    "OcrLineEnding",
    "OcrSkill",
    "OcrSkillLanguage",
    "OutputFieldMappingEntry",
    "PathHierarchyTokenizer",
    "PatternAnalyzer",
    "PatternCaptureTokenFilter",
    "PatternReplaceCharFilter",
    "PatternReplaceTokenFilter",
    "PatternTokenizer",
    "PIIDetectionSkill",
    "PIIDetectionSkillMaskingMode",
    "PhoneticEncoder",
    "PhoneticTokenFilter",
    "RegexFlags",
    "ScalarQuantizationCompression",
    "ScalarQuantizationParameters",
    "ScoringFunction",
    "ScoringFunctionAggregation",
    "ScoringFunctionInterpolation",
    "ScoringProfile",
    "SearchableField",
    "SearchField",
    "SearchIndex",
    "SearchIndexer",
    "SearchIndexerDataContainer",
    "SearchIndexerDataIdentity",
    "SearchIndexerDataNoneIdentity",
    "SearchIndexerDataUserAssignedIdentity",
    "SearchIndexerDataSourceConnection",
    "SearchIndexerDataSourceType",
    "SearchIndexerError",
    "SearchIndexerIndexProjection",
    "SearchIndexerIndexProjectionSelector",
    "SearchIndexerIndexProjectionsParameters",
    "SearchIndexerKnowledgeStore",
    "SearchIndexerKnowledgeStoreBlobProjectionSelector",
    "SearchIndexerKnowledgeStoreFileProjectionSelector",
    "SearchIndexerKnowledgeStoreObjectProjectionSelector",
    "SearchIndexerKnowledgeStoreProjection",
    "SearchIndexerKnowledgeStoreProjectionSelector",
    "SearchIndexerKnowledgeStoreTableProjectionSelector",
    "SearchIndexerLimits",
    "SearchIndexerSkill",
    "SearchIndexerSkillset",
    "SearchIndexerStatus",
    "SearchIndexerWarning",
    "SearchResourceEncryptionKey",
    "SearchServiceCounters",
    "SearchServiceLimits",
    "SearchServiceStatistics",
    "SearchSuggester",
    "SemanticConfiguration",
    "SemanticField",
    "SemanticPrioritizedFields",
    "SemanticSearch",
    "SentimentSkill",
    "SentimentSkillLanguage",
    "SentimentSkillVersion",
    "ShaperSkill",
    "ShingleTokenFilter",
    "SimpleField",
    "SimilarityAlgorithm",
    "SnowballTokenFilter",
    "SnowballTokenFilterLanguage",
    "SoftDeleteColumnDeletionDetectionPolicy",
    "SplitSkill",
    "SplitSkillLanguage",
    "SqlIntegratedChangeTrackingPolicy",
    "StemmerOverrideTokenFilter",
    "StemmerTokenFilter",
    "StemmerTokenFilterLanguage",
    "StopAnalyzer",
    "StopwordsList",
    "StopwordsTokenFilter",
    "SuggestOptions",
    "SynonymMap",
    "SynonymTokenFilter",
    "TagScoringFunction",
    "TagScoringParameters",
    "TextSplitMode",
    "TextTranslationSkill",
    "TextTranslationSkillLanguage",
    "TextWeights",
    "TokenCharacterKind",
    "TokenFilter",
    "TokenFilterName",
    "TruncateTokenFilter",
    "UaxUrlEmailTokenizer",
    "UniqueTokenFilter",
    "VectorEncodingFormat",
    "VectorSearch",
    "VectorSearchAlgorithmConfiguration",
    "VectorSearchAlgorithmKind",
    "VectorSearchAlgorithmMetric",
    "VectorSearchCompression",
    "VectorSearchCompressionTarget",
    "VectorSearchProfile",
    "VectorSearchVectorizer",
    "VectorSearchVectorizerKind",
    "VisualFeature",
    "WebApiSkill",
    "WebApiVectorizer",
    "WebApiVectorizerParameters",
    "WordDelimiterTokenFilter",
    "SearchFieldDataType",
)
