#!/usr/bin/env python
"""
Test script to verify AutoGen 0.6.2 integration with Django
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).resolve().parent
sys.path.append(str(project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_autogen.settings')
django.setup()

import asyncio
from django.contrib.auth.models import User
from agenticai.models import AgentProfile, Team
from agenticai.autogen_service import autogen_service


async def test_agent_creation():
    """Test creating and using an AutoGen agent"""
    print("🧪 Testing AutoGen 0.6.2 Integration...")
    
    try:
        # Get the admin user
        user = User.objects.get(username='admin')
        print(f"✓ Found user: {user.username}")
        
        # Get a sample agent
        agent_profile = AgentProfile.objects.filter(created_by=user).first()
        if not agent_profile:
            print("❌ No agent profiles found. Run 'python manage.py create_sample_data' first.")
            return False
        
        print(f"✓ Found agent profile: {agent_profile.name}")
        
        # Test creating an AutoGen agent
        try:
            autogen_agent = autogen_service.create_agent(agent_profile)
            print(f"✓ Successfully created AutoGen agent: {autogen_agent.name}")
        except Exception as e:
            print(f"❌ Failed to create AutoGen agent: {str(e)}")
            return False
        
        # Test creating a team
        team = Team.objects.filter(created_by=user).first()
        if team:
            print(f"✓ Found team: {team.name}")
            try:
                autogen_team = autogen_service.create_team(team)
                print(f"✓ Successfully created AutoGen team with {len(autogen_team.participants)} participants")
            except Exception as e:
                print(f"❌ Failed to create AutoGen team: {str(e)}")
                return False
        
        print("✅ AutoGen 0.6.2 integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False


async def test_simple_conversation():
    """Test a simple conversation with AutoGen"""
    print("\n🗣️ Testing simple conversation...")
    
    try:
        from autogen_agentchat.agents import AssistantAgent
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        
        # Create a simple model client
        model_client = OpenAIChatCompletionClient(
            model="gpt-3.5-turbo",
            api_key=os.getenv('OPENAI_API_KEY')
        )
        
        # Create a simple agent
        agent = AssistantAgent(
            name="test_agent",
            model_client=model_client,
            system_message="You are a helpful assistant. Respond briefly."
        )
        
        print("✓ Created test agent successfully")
        print("✅ Simple conversation test completed!")
        
        # Close the model client
        await model_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Simple conversation test failed: {str(e)}")
        return False


def main():
    """Main test function"""
    print("🚀 Starting Django AutoGen 0.6.2 Integration Tests\n")
    
    # Check if OpenAI API key is available
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY not found in environment variables")
        print("Please make sure your .env file contains the OpenAI API key")
        return
    
    print("✓ OpenAI API key found in environment")
    
    # Run async tests
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # Test agent creation
        success1 = loop.run_until_complete(test_agent_creation())
        
        # Test simple conversation
        success2 = loop.run_until_complete(test_simple_conversation())
        
        if success1 and success2:
            print("\n🎉 All tests passed! Your Django AutoGen 0.6.2 setup is working correctly.")
            print("\nNext steps:")
            print("1. Start the Django server: python manage.py runserver")
            print("2. Access the admin interface: http://localhost:8000/admin/ (admin/admin123)")
            print("3. Test the API endpoints: http://localhost:8000/api/")
        else:
            print("\n❌ Some tests failed. Please check the error messages above.")
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")
    finally:
        loop.close()


if __name__ == "__main__":
    main()
