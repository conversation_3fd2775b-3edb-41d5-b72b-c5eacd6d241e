# Sitemap Service API - Browser Access Guide

The Django AutoGen project provides REST API endpoints to access the sitemap parsing service directly through browser URLs. No authentication is required for sitemap analysis endpoints.

## 🌐 Base URL

```
http://localhost:8000/api/sitemap/
```

## 📋 Available Endpoints

### 1. Extract URLs from Sitemap

**Endpoint:** `GET /api/sitemap/extract_urls/`

Extract URLs from a specific sitemap with metadata.

**Parameters:**
- `sitemap_url` (required): Direct URL to the sitemap XML file
- `max_urls` (optional): Maximum number of URLs to return (default: 50)

**Browser URL Examples:**
```
http://localhost:8000/api/sitemap/extract_urls/?sitemap_url=https://www.sitemaps.org/sitemap.xml&max_urls=5

http://localhost:8000/api/sitemap/extract_urls/?sitemap_url=https://example.com/sitemap.xml&max_urls=20
```

**Response Format:**
```json
{
  "sitemap_url": "https://www.sitemaps.org/sitemap.xml",
  "total_urls": 5,
  "max_urls_requested": 5,
  "urls": [
    {
      "url": "https://www.sitemaps.org/",
      "last_modified": "2016-11-21T00:00:00",
      "change_frequency": null,
      "priority": 0.5
    },
    {
      "url": "https://www.sitemaps.org/protocol.html",
      "last_modified": "2022-12-15T00:00:00",
      "change_frequency": null,
      "priority": 0.5
    }
  ]
}
```

### 2. Discover Website Sitemaps

**Endpoint:** `GET /api/sitemap/discover_sitemaps/`

Automatically discover all sitemaps for a website.

**Parameters:**
- `website_url` (required): Base URL of the website

**Browser URL Examples:**
```
http://localhost:8000/api/sitemap/discover_sitemaps/?website_url=https://www.sitemaps.org

http://localhost:8000/api/sitemap/discover_sitemaps/?website_url=https://example.com
```

**Response Format:**
```json
{
  "website_url": "https://www.sitemaps.org",
  "sitemaps_found": 1,
  "sitemaps": [
    "https://www.sitemaps.org/sitemap.xml"
  ]
}
```

### 3. Complete Website Analysis

**Endpoint:** `GET /api/sitemap/analyze_website/`

Perform complete website analysis including sitemap discovery and URL extraction.

**Parameters:**
- `website_url` (required): Base URL of the website
- `max_urls` (optional): Maximum URLs to extract per sitemap (default: 50)

**Browser URL Examples:**
```
http://localhost:8000/api/sitemap/analyze_website/?website_url=https://www.sitemaps.org&max_urls=10

http://localhost:8000/api/sitemap/analyze_website/?website_url=https://news.ycombinator.com&max_urls=25
```

**Response Format:**
```json
{
  "base_url": "https://www.sitemaps.org",
  "sitemaps_found": 1,
  "total_urls": 10,
  "sitemaps": [
    {
      "sitemap_url": "https://www.sitemaps.org/sitemap.xml",
      "urls": [...]
    }
  ],
  "all_urls": [...],
  "url_patterns": {
    "total_urls": 10,
    "domains": ["www.sitemaps.org"],
    "path_patterns": {
      "protocol.html": 1,
      "faq.html": 1,
      "da": 4,
      "de": 2
    },
    "file_extensions": {
      "html": 7
    },
    "url_depths": {
      "0": 1,
      "1": 5,
      "2": 4
    }
  }
}
```

## 🔧 Usage Examples

### Example 1: Analyze a News Website
```
http://localhost:8000/api/sitemap/analyze_website/?website_url=https://news.ycombinator.com&max_urls=20
```

### Example 2: Extract E-commerce Product URLs
```
http://localhost:8000/api/sitemap/extract_urls/?sitemap_url=https://shop.example.com/sitemap-products.xml&max_urls=50
```

### Example 3: Discover Blog Sitemaps
```
http://localhost:8000/api/sitemap/discover_sitemaps/?website_url=https://blog.example.com
```

### Example 4: Quick Sitemap Check
```
http://localhost:8000/api/sitemap/extract_urls/?sitemap_url=https://www.python.org/sitemap.xml&max_urls=10
```

## 🌐 Browser Testing

You can test these endpoints directly in your browser by copying and pasting the URLs. The API returns JSON responses that browsers will display.

### Testing Steps:

1. **Start the Django server:**
   ```bash
   python manage.py runserver
   ```

2. **Open your browser and navigate to any of the endpoint URLs above**

3. **The browser will display the JSON response**

## 📱 Using with JavaScript/AJAX

You can also call these endpoints from JavaScript:

```javascript
// Extract URLs from sitemap
fetch('http://localhost:8000/api/sitemap/extract_urls/?sitemap_url=https://www.sitemaps.org/sitemap.xml&max_urls=5')
  .then(response => response.json())
  .then(data => {
    console.log('Found', data.total_urls, 'URLs');
    data.urls.forEach(url => {
      console.log(url.url, '- Priority:', url.priority);
    });
  });

// Discover sitemaps
fetch('http://localhost:8000/api/sitemap/discover_sitemaps/?website_url=https://example.com')
  .then(response => response.json())
  .then(data => {
    console.log('Found', data.sitemaps_found, 'sitemaps');
    data.sitemaps.forEach(sitemap => console.log(sitemap));
  });

// Complete website analysis
fetch('http://localhost:8000/api/sitemap/analyze_website/?website_url=https://example.com&max_urls=20')
  .then(response => response.json())
  .then(data => {
    console.log('Website analysis:', data);
    console.log('Total URLs:', data.total_urls);
    console.log('URL patterns:', data.url_patterns);
  });
```

## 🛠️ Using with curl

For command-line testing:

```bash
# Extract URLs
curl "http://localhost:8000/api/sitemap/extract_urls/?sitemap_url=https://www.sitemaps.org/sitemap.xml&max_urls=5"

# Discover sitemaps
curl "http://localhost:8000/api/sitemap/discover_sitemaps/?website_url=https://www.sitemaps.org"

# Analyze website
curl "http://localhost:8000/api/sitemap/analyze_website/?website_url=https://www.sitemaps.org&max_urls=10"
```

## ⚠️ Error Handling

The API returns appropriate HTTP status codes and error messages:

**400 Bad Request** - Missing required parameters:
```json
{
  "error": "sitemap_url parameter is required"
}
```

**500 Internal Server Error** - Processing errors:
```json
{
  "error": "Failed to extract URLs from sitemap: Invalid XML format"
}
```

## 🔒 Security Notes

- **No Authentication Required**: Sitemap endpoints are publicly accessible
- **Rate Limiting**: Consider implementing rate limiting for production use
- **URL Validation**: The service validates URLs but doesn't restrict domains
- **CORS**: Configure CORS settings if accessing from different domains

## 🚀 Production Deployment

For production use:

1. **Configure CORS** in Django settings for cross-origin requests
2. **Add rate limiting** to prevent abuse
3. **Use HTTPS** for secure communication
4. **Monitor usage** and implement caching if needed

## 📊 Response Data Explanation

### URL Object Fields:
- `url`: The actual webpage URL
- `last_modified`: When the page was last updated (ISO format)
- `change_frequency`: How often the page changes (daily, weekly, etc.)
- `priority`: Page priority (0.0 to 1.0)

### URL Patterns Analysis:
- `domains`: List of domains found in URLs
- `path_patterns`: Count of URLs by first path segment
- `file_extensions`: Count of files by extension
- `url_depths`: Count of URLs by path depth level

This API provides a simple, browser-accessible way to analyze website sitemaps without requiring any special tools or authentication!
