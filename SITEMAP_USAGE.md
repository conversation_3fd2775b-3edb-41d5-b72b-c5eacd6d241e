# Sitemap Parsing with ultimate-sitemap-parser

The Django AutoGen project now includes sitemap parsing capabilities using the `ultimate-sitemap-parser` package. This feature allows you to extract and analyze URLs from website sitemaps.

## Features

- **Extract URLs from sitemaps** with metadata (last modified, priority, change frequency)
- **Discover sitemaps** automatically from website homepages
- **Analyze website structure** by parsing all discovered sitemaps
- **Management command** for easy command-line usage
- **Standalone functions** for programmatic access

## Installation

The `ultimate-sitemap-parser` package is already included in the project requirements.

## Usage

### 1. Management Command

Use the Django management command to analyze sitemaps from the command line:

```bash
# Analyze a direct sitemap URL
python manage.py analyze_sitemap https://www.sitemaps.org/sitemap.xml --sitemap-only --max-urls 10

# Discover sitemaps for a website
python manage.py analyze_sitemap https://example.com --discover-only

# Full website analysis (discover + extract)
python manage.py analyze_sitemap https://example.com --max-urls 50

# Save results to JSON file
python manage.py analyze_sitemap https://example.com --output-json results.json
```

### 2. Programmatic Usage

Use the sitemap service directly in your Python code:

```python
from agenticai.sitemap_service import (
    get_website_sitemap_analysis,
    extract_urls_from_sitemap,
    discover_website_sitemaps
)

# Extract URLs from a specific sitemap
urls = extract_urls_from_sitemap('https://www.sitemaps.org/sitemap.xml', max_urls=20)
for url_info in urls:
    print(f"URL: {url_info['url']}")
    print(f"Last Modified: {url_info['last_modified']}")
    print(f"Priority: {url_info['priority']}")

# Discover all sitemaps for a website
sitemaps = discover_website_sitemaps('https://example.com')
print(f"Found {len(sitemaps)} sitemaps")

# Complete website analysis
analysis = get_website_sitemap_analysis('https://example.com', max_urls=100)
print(f"Total URLs: {analysis['total_urls']}")
print(f"Sitemaps found: {analysis['sitemaps_found']}")
```

### 3. Service Class Usage

For more advanced usage, use the service class directly:

```python
from agenticai.sitemap_service import sitemap_service

# Extract URLs with custom limits
urls = sitemap_service.extract_sitemap_urls(
    'https://www.sitemaps.org/sitemap.xml', 
    max_urls=50
)

# Discover sitemaps
sitemaps = sitemap_service.discover_sitemap_urls('https://example.com')

# Full website structure analysis
analysis = sitemap_service.analyze_website_structure(
    'https://example.com', 
    max_urls_per_sitemap=100
)
```

## Data Structure

### URL Information

Each extracted URL contains the following metadata:

```python
{
    'url': 'https://example.com/page',
    'last_modified': '2023-01-15T10:30:00',  # ISO format or None
    'change_frequency': 'weekly',             # or None
    'priority': 0.8                          # float or None
}
```

### Website Analysis

Complete website analysis returns:

```python
{
    'base_url': 'https://example.com',
    'sitemaps_found': 3,
    'total_urls': 150,
    'sitemaps': [
        {
            'sitemap_url': 'https://example.com/sitemap.xml',
            'urls': [...]  # List of URL objects
        }
    ],
    'all_urls': [...],  # All unique URLs from all sitemaps
    'url_patterns': {
        'total_urls': 150,
        'domains': ['example.com'],
        'path_patterns': {'blog': 45, 'products': 30, ...},
        'file_extensions': {'html': 120, 'pdf': 15, ...},
        'url_depths': {1: 20, 2: 80, 3: 50}
    }
}
```

## Command Line Options

The `analyze_sitemap` management command supports these options:

- `url` (required): Website URL or direct sitemap URL
- `--max-urls N`: Maximum URLs to extract (default: 50)
- `--sitemap-only`: Treat URL as direct sitemap instead of homepage
- `--discover-only`: Only discover sitemaps without extracting content
- `--output-json FILE`: Save results to JSON file

## Examples

### Example 1: Analyze a News Website

```bash
python manage.py analyze_sitemap https://news.ycombinator.com --max-urls 20
```

### Example 2: Extract E-commerce Product URLs

```python
from agenticai.sitemap_service import extract_urls_from_sitemap

# Extract product URLs from an e-commerce sitemap
product_urls = extract_urls_from_sitemap(
    'https://shop.example.com/sitemap-products.xml',
    max_urls=100
)

# Filter for product pages
products = [url for url in product_urls if '/product/' in url['url']]
print(f"Found {len(products)} product pages")
```

### Example 3: Monitor Website Changes

```python
from agenticai.sitemap_service import get_website_sitemap_analysis
import json
from datetime import datetime

# Analyze website structure
analysis = get_website_sitemap_analysis('https://blog.example.com')

# Save analysis with timestamp
result = {
    'timestamp': datetime.now().isoformat(),
    'analysis': analysis
}

with open(f"analysis_{datetime.now().strftime('%Y%m%d')}.json", 'w') as f:
    json.dump(result, f, indent=2)
```

## Integration with AutoGen Agents

The sitemap parsing functionality can be integrated with AutoGen agents for web research tasks:

```python
# This could be added as a tool for AutoGen agents
async def analyze_website_structure(website_url: str) -> str:
    """Tool for AutoGen agents to analyze website structure"""
    from agenticai.sitemap_service import get_website_sitemap_analysis
    
    try:
        analysis = get_website_sitemap_analysis(website_url, max_urls=50)
        
        result = f"Website Analysis for {website_url}:\n"
        result += f"- Sitemaps found: {analysis['sitemaps_found']}\n"
        result += f"- Total URLs: {analysis['total_urls']}\n"
        
        if analysis['url_patterns']['path_patterns']:
            result += "\nTop content categories:\n"
            for pattern, count in list(analysis['url_patterns']['path_patterns'].items())[:5]:
                result += f"- /{pattern}/: {count} pages\n"
        
        return result
    except Exception as e:
        return f"Error analyzing website: {str(e)}"
```

## Error Handling

The sitemap service includes comprehensive error handling:

- Invalid sitemap URLs return empty results
- Network timeouts are handled gracefully
- Malformed XML sitemaps are skipped with warnings
- Rate limiting prevents overwhelming target servers

## Limitations

- Some websites may block automated sitemap requests
- Very large sitemaps may take time to process
- The service respects robots.txt but doesn't implement all crawling etiquette
- Sitemap index files are supported but may require multiple requests

## Next Steps

This sitemap parsing functionality provides a foundation for:

1. **Web scraping workflows** - Discover pages to scrape
2. **Content analysis** - Understand website structure
3. **SEO analysis** - Analyze sitemap optimization
4. **Competitive research** - Study competitor website structures
5. **AutoGen agent tools** - Provide web research capabilities to AI agents
