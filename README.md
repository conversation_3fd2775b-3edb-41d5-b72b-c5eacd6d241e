# Django AutoGen 0.6.2 - AgenticAI Application

A Django-based web application for building and managing AgenticAI applications using Microsoft AutoGen 0.6.2.

## Features

- **Agent Management**: Create and configure AutoGen agents with different types (Assistant, User Proxy, Custom)
- **Team Management**: Build teams of agents with various coordination patterns (Round Robin, Selector Group Chat, Swarm, Graph Flow)
- **Conversation Tracking**: Monitor and manage conversations between agents
- **Task Management**: Assign and execute tasks using AutoGen teams
- **REST API**: Full REST API for integration with frontend applications
- **Admin Interface**: Django admin interface for easy management

## AutoGen 0.6.2 Integration

This project follows the official AutoGen 0.6.2 documentation and uses:
- `autogen-agentchat` for high-level agent and team management
- `autogen-ext[openai,azure]` for model client integrations
- Support for OpenAI and Azure OpenAI models
- Async conversation execution
- Tool/function calling capabilities

## Installation

### 1. Clone the repository
```bash
git clone <repository-url>
cd django-autogen
```

### 2. Set up virtual environment
```bash
python3 -m venv django_autogen_env
source django_autogen_env/bin/activate  # On Windows: django_autogen_env\Scripts\activate
```

### 3. Install dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment configuration
```bash
cp .env.example .env
# Edit .env file with your API keys and configuration
```

### 5. Database setup
```bash
python manage.py migrate
python manage.py createsuperuser
```

### 6. Run the development server
```bash
python manage.py runserver
```

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure the following:

- **OPENAI_API_KEY**: Your OpenAI API key
- **AZURE_OPENAI_API_KEY**: Your Azure OpenAI API key (if using Azure)
- **AZURE_OPENAI_ENDPOINT**: Your Azure OpenAI endpoint (if using Azure)
- **ANTHROPIC_API_KEY**: Your Anthropic API key (if using Claude models)

### Model Configuration

Agents can be configured with different model clients:

```json
{
  "type": "openai",
  "model": "gpt-4",
  "api_key": "your-api-key"
}
```

Or for Azure OpenAI:

```json
{
  "type": "azure_openai",
  "model": "gpt-4",
  "api_key": "your-azure-api-key",
  "endpoint": "https://your-resource.openai.azure.com/"
}
```

## API Endpoints

### Agents
- `GET /api/agents/` - List all agents
- `POST /api/agents/` - Create a new agent
- `GET /api/agents/{id}/` - Get agent details
- `PUT /api/agents/{id}/` - Update agent
- `DELETE /api/agents/{id}/` - Delete agent
- `POST /api/agents/{id}/test_agent/` - Test agent configuration

### Teams
- `GET /api/teams/` - List all teams
- `POST /api/teams/` - Create a new team
- `GET /api/teams/{id}/` - Get team details
- `PUT /api/teams/{id}/` - Update team
- `DELETE /api/teams/{id}/` - Delete team
- `POST /api/teams/{id}/start_conversation/` - Start conversation with team

### Conversations
- `GET /api/conversations/` - List all conversations
- `GET /api/conversations/{id}/` - Get conversation details
- `GET /api/conversations/{id}/messages/` - Get conversation messages
- `POST /api/conversations/{id}/continue_conversation/` - Continue conversation

### Tasks
- `GET /api/tasks/` - List all tasks
- `POST /api/tasks/` - Create a new task
- `GET /api/tasks/{id}/` - Get task details
- `PUT /api/tasks/{id}/` - Update task
- `POST /api/tasks/{id}/execute/` - Execute task
- `GET /api/tasks/by_status/?status=pending` - Filter tasks by status

## Usage Examples

### Creating an Agent

```python
import requests

agent_data = {
    "name": "Research Assistant",
    "agent_type": "assistant",
    "system_message": "You are a helpful research assistant.",
    "model_config": {
        "type": "openai",
        "model": "gpt-4"
    },
    "tools": []
}

response = requests.post('http://localhost:8000/api/agents/', 
                        json=agent_data,
                        headers={'Authorization': 'Token your-token'})
```

### Creating a Team

```python
team_data = {
    "name": "Research Team",
    "team_type": "round_robin",
    "description": "A team for research tasks",
    "agent_ids": [1, 2, 3],  # IDs of agents to include
    "max_turns": 10
}

response = requests.post('http://localhost:8000/api/teams/', 
                        json=team_data,
                        headers={'Authorization': 'Token your-token'})
```

### Starting a Conversation

```python
conversation_data = {
    "message": "Please research the latest developments in AI",
    "title": "AI Research Task"
}

response = requests.post('http://localhost:8000/api/teams/1/start_conversation/', 
                        json=conversation_data,
                        headers={'Authorization': 'Token your-token'})
```

## Development

### Project Structure

```
django-autogen/
├── django_autogen/          # Django project settings
├── agenticai/              # Main application
│   ├── models.py          # Database models
│   ├── views.py           # API views
│   ├── serializers.py     # DRF serializers
│   ├── autogen_service.py # AutoGen integration
│   └── admin.py           # Admin configuration
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
└── README.md              # This file
```

### Running Tests

```bash
python manage.py test
```

### Admin Interface

Access the Django admin at `http://localhost:8000/admin/` to manage agents, teams, conversations, and tasks through a web interface.

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions, please open an issue on the GitHub repository.
