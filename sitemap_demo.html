<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sitemap Service API Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .url-list {
            margin-top: 10px;
        }
        .url-item {
            padding: 8px;
            margin: 5px 0;
            background: #e9ecef;
            border-radius: 3px;
            font-size: 11px;
        }
        .loading {
            color: #007cba;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Sitemap Service API Demo</h1>
        <p style="text-align: center; color: #666;">
            Test the Django AutoGen sitemap parsing API endpoints directly from your browser
        </p>

        <!-- Extract URLs Section -->
        <div class="section">
            <h2>1. Extract URLs from Sitemap</h2>
            <p>Extract URLs from a specific sitemap XML file.</p>
            
            <div class="form-group">
                <label for="sitemap-url">Sitemap URL:</label>
                <input type="text" id="sitemap-url" value="https://www.sitemaps.org/sitemap.xml" placeholder="https://example.com/sitemap.xml">
            </div>
            
            <div class="form-group">
                <label for="max-urls-extract">Max URLs:</label>
                <input type="number" id="max-urls-extract" value="10" min="1" max="100">
            </div>
            
            <button onclick="extractUrls()">Extract URLs</button>
            <div id="extract-result" class="result" style="display: none;"></div>
        </div>

        <!-- Discover Sitemaps Section -->
        <div class="section">
            <h2>2. Discover Website Sitemaps</h2>
            <p>Automatically discover all sitemaps for a website.</p>
            
            <div class="form-group">
                <label for="website-url-discover">Website URL:</label>
                <input type="text" id="website-url-discover" value="https://www.sitemaps.org" placeholder="https://example.com">
            </div>
            
            <button onclick="discoverSitemaps()">Discover Sitemaps</button>
            <div id="discover-result" class="result" style="display: none;"></div>
        </div>

        <!-- Analyze Website Section -->
        <div class="section">
            <h2>3. Complete Website Analysis</h2>
            <p>Perform complete website analysis including sitemap discovery and URL extraction.</p>
            
            <div class="form-group">
                <label for="website-url-analyze">Website URL:</label>
                <input type="text" id="website-url-analyze" value="https://www.sitemaps.org" placeholder="https://example.com">
            </div>
            
            <div class="form-group">
                <label for="max-urls-analyze">Max URLs per Sitemap:</label>
                <input type="number" id="max-urls-analyze" value="15" min="1" max="100">
            </div>
            
            <button onclick="analyzeWebsite()">Analyze Website</button>
            <div id="analyze-result" class="result" style="display: none;"></div>
        </div>

        <!-- Quick Test Links -->
        <div class="section">
            <h2>🔗 Quick Test Links</h2>
            <p>Click these links to test the API directly in your browser:</p>
            <ul>
                <li><a href="http://localhost:8000/api/sitemap/extract_urls/?sitemap_url=https://www.sitemaps.org/sitemap.xml&max_urls=5" target="_blank">Extract 5 URLs from sitemaps.org</a></li>
                <li><a href="http://localhost:8000/api/sitemap/discover_sitemaps/?website_url=https://www.sitemaps.org" target="_blank">Discover sitemaps for sitemaps.org</a></li>
                <li><a href="http://localhost:8000/api/sitemap/analyze_website/?website_url=https://www.sitemaps.org&max_urls=10" target="_blank">Analyze sitemaps.org website</a></li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/sitemap';

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result loading';
            element.textContent = 'Loading...';
        }

        async function extractUrls() {
            const sitemapUrl = document.getElementById('sitemap-url').value;
            const maxUrls = document.getElementById('max-urls-extract').value;
            
            if (!sitemapUrl) {
                showResult('extract-result', { error: 'Please enter a sitemap URL' }, true);
                return;
            }

            showLoading('extract-result');
            
            try {
                const response = await fetch(`${API_BASE}/extract_urls/?sitemap_url=${encodeURIComponent(sitemapUrl)}&max_urls=${maxUrls}`);
                const data = await response.json();
                showResult('extract-result', data, !response.ok);
            } catch (error) {
                showResult('extract-result', { error: error.message }, true);
            }
        }

        async function discoverSitemaps() {
            const websiteUrl = document.getElementById('website-url-discover').value;
            
            if (!websiteUrl) {
                showResult('discover-result', { error: 'Please enter a website URL' }, true);
                return;
            }

            showLoading('discover-result');
            
            try {
                const response = await fetch(`${API_BASE}/discover_sitemaps/?website_url=${encodeURIComponent(websiteUrl)}`);
                const data = await response.json();
                showResult('discover-result', data, !response.ok);
            } catch (error) {
                showResult('discover-result', { error: error.message }, true);
            }
        }

        async function analyzeWebsite() {
            const websiteUrl = document.getElementById('website-url-analyze').value;
            const maxUrls = document.getElementById('max-urls-analyze').value;
            
            if (!websiteUrl) {
                showResult('analyze-result', { error: 'Please enter a website URL' }, true);
                return;
            }

            showLoading('analyze-result');
            
            try {
                const response = await fetch(`${API_BASE}/analyze_website/?website_url=${encodeURIComponent(websiteUrl)}&max_urls=${maxUrls}`);
                const data = await response.json();
                showResult('analyze-result', data, !response.ok);
            } catch (error) {
                showResult('analyze-result', { error: error.message }, true);
            }
        }

        // Add some example URLs for testing
        const examples = [
            'https://www.sitemaps.org/sitemap.xml',
            'https://www.python.org/sitemap.xml',
            'https://docs.python.org/sitemap.xml'
        ];

        // You can add more interactive features here
    </script>
</body>
</html>
